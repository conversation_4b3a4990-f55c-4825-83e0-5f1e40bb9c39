import { StatusBar } from 'expo-status-bar';
import { Clerk<PERSON><PERSON><PERSON>, ClerkLoaded } from '@clerk/clerk-expo';
import { tokenCache } from './src/cache';
import AuthenticatedApp from './src/components/AuthenticatedApp';

const publishableKey = process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY!;

if (!publishableKey) {
  throw new Error(
    'Missing Publishable Key. Please set EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY in your .env'
  );
}

export default function App() {
  return (
    <ClerkProvider tokenCache={tokenCache} publishableKey={publishableKey}>
      <ClerkLoaded>
        <AuthenticatedApp />
        <StatusBar style="auto" />
      </ClerkLoaded>
    </ClerkProvider>
  );
}
