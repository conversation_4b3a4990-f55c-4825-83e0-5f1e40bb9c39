{"name": "reactnativeauthentication-main", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@clerk/clerk-expo": "^2.14.2", "@expo/metro-runtime": "^5.0.4", "expo": "~53.0.17", "expo-auth-session": "^6.2.1", "expo-crypto": "^14.1.5", "expo-secure-store": "~14.2.3", "expo-status-bar": "~2.2.3", "expo-web-browser": "^14.2.0", "react": "19.0.0", "react-dom": "^19.1.0", "react-native": "0.79.5", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}