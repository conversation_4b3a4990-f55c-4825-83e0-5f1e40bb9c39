{"name": "reactnativeauthentication", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@clerk/clerk-expo": "^2.14.2", "@hookform/resolvers": "^5.1.1", "expo": "^53.0.18", "expo-auth-session": "~6.2.1", "expo-constants": "~17.1.7", "expo-crypto": "~14.1.5", "expo-linking": "~7.1.7", "expo-router": "~5.1.3", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "expo-web-browser": "~14.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-native": "^0.79.5", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "~0.20.0", "zod": "^3.25.76"}, "devDependencies": {"@babel/core": "^7.28.0", "@types/react": "~19.0.10", "typescript": "^5.8.3"}, "private": true}