import { Platform } from 'react-native';

// Simple token cache implementation
const createTokenCache = () => {
  return {
    getToken: async (key: string) => {
      try {
        if (Platform.OS === 'web') {
          return localStorage.getItem(key);
        }
        // For mobile, we'll use a simple in-memory cache for now
        // In production, you should use expo-secure-store
        return null;
      } catch (error) {
        console.error('Error getting token:', error);
        return null;
      }
    },
    saveToken: async (key: string, token: string) => {
      try {
        if (Platform.OS === 'web') {
          return localStorage.setItem(key, token);
        }
        // For mobile, we'll use a simple in-memory cache for now
        // In production, you should use expo-secure-store
      } catch (error) {
        console.error('Error saving token:', error);
      }
    },
  };
};

export const tokenCache = createTokenCache();
